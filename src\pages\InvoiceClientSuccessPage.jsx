import React, { useEffect, useState } from "react";
import { useSearchParams, Link, useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "Src/globalContext";
import { AuthContext } from "Src/authContext";

const InvoiceClientSuccessPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state: authState } = React.useContext(AuthContext);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [paymentData, setPaymentData] = useState(null);
  const [error, setError] = useState(null);

  // Get invoice_id from URL parameters
  const invoiceId = searchParams.get("invoice_id");

  useEffect(() => {
    if (!invoiceId) {
      setError("No invoice ID found");
      setLoading(false);
      return;
    }

    // Just show success - no need to fetch payment details
    setPaymentData({ invoiceId });
    setLoading(false);
  }, [invoiceId]);

  const handleViewInvoices = () => {
    // Check if user is authenticated
    if (!authState.isAuthenticated) {
      return;
    } else {
      navigate("/client/invoices");
    }
  };

  const handleViewInvoice = () => {
    // Check if user is authenticated
    if (!authState.isAuthenticated) {
      return;
    } else {
      // If logged in, redirect to the specific invoice page
      navigate(`/client/invoice/${invoiceId}`);
    }
  };

  const handleViewDashboard = () => {
    // Check if user is authenticated
    if (!authState.isAuthenticated) {
      return;
    } else {
      navigate("/client/projects");
    }
  };

  if (loading) {
    return (
      <div className="container p-4 mx-auto">
        <div className="p-8 mx-auto max-w-2xl rounded-lg border border-strokedark bg-boxdark">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="w-12 h-12 rounded-full border-4 animate-spin border-primary border-t-transparent"></div>
            </div>
            <p className="text-white">Verifying your payment...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container p-4 mx-auto">
        <div className="p-8 mx-auto max-w-2xl rounded-lg border border-strokedark bg-boxdark">
          <div className="mb-8 text-center">
            <div className="flex justify-center mb-4">
              <div className="p-4 rounded-full bg-danger/20">
                <svg
                  className="w-12 h-12 text-danger"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  ></path>
                </svg>
              </div>
            </div>
            <h1 className="mb-2 text-3xl font-bold text-white">
              Payment Error
            </h1>
            <p className="text-lg text-bodydark">{error}</p>
          </div>
          <div className="text-center">
            <button
              onClick={handleViewInvoices}
              className="inline-flex items-center px-6 py-3 text-white rounded-md bg-primary hover:bg-opacity-90"
            >
              View Invoices
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container p-4 mx-auto">
      <div className="p-8 mx-auto max-w-2xl rounded-lg border border-strokedark bg-boxdark">
        <div className="mb-8 text-center">
          <div className="flex justify-center mb-4">
            <div className="p-4 rounded-full bg-success/20">
              <svg
                className="w-12 h-12 text-success"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
            </div>
          </div>
          <h1 className="mb-2 text-3xl font-bold text-white">
            Payment Successful!
          </h1>
          <p className="text-lg text-bodydark">
            Thank you for your payment. Your transaction has been completed
            successfully.
          </p>
        </div>

        <div className="p-6 mb-8 rounded-lg border border-stroke bg-meta-4/20">
          <h2 className="mb-4 text-xl font-semibold text-white">
            Payment Details
          </h2>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-bodydark">Invoice ID:</span>
              <span className="font-mono text-sm text-white">{invoiceId}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-bodydark">Payment Method:</span>
              <span className="text-white">Stripe Checkout</span>
            </div>
            <div className="flex justify-between">
              <span className="text-bodydark">Status:</span>
              <span className="font-semibold text-success">Completed</span>
            </div>
          </div>
        </div>

        <div className="p-6 mb-8 rounded-lg border border-stroke bg-meta-4/20">
          <h3 className="mb-4 text-lg font-semibold text-white">
            What happens next?
          </h3>
          <ul className="space-y-2 text-bodydark">
            <li className="flex items-start">
              <span className="mr-2 text-success">•</span>
              You will receive a confirmation email shortly
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-success">•</span>
              We'll begin processing your order right away
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-success">•</span>
              You can track your order status in your dashboard
            </li>
          </ul>
        </div>

        <div className="flex flex-col gap-4 justify-center sm:flex-row">
          {authState.isAuthenticated ? (
            <>
              <button
                onClick={handleViewInvoice}
                className="inline-flex justify-center items-center px-6 py-3 text-white rounded-md bg-primary hover:bg-opacity-90"
              >
                View This Invoice
              </button>
              <button
                onClick={handleViewInvoices}
                className="inline-flex justify-center items-center px-6 py-3 text-white rounded-md border border-stroke hover:bg-meta-4"
              >
                View All Invoices
              </button>
              <button
                onClick={handleViewDashboard}
                className="inline-flex justify-center items-center px-6 py-3 text-white rounded-md border border-stroke hover:bg-meta-4"
              >
                Go to Dashboard
              </button>
            </>
          ) : (
            <div className="text-center">
              <p className="mb-4 text-bodydark">
                Your payment was successful! Thank you for your business.
              </p>
              <button
                onClick={() => window.close()}
                className="inline-flex justify-center items-center px-6 py-3 text-white rounded-md bg-primary hover:bg-opacity-90"
              >
                Close
              </button>
            </div>
          )}
        </div>

        <div className="mt-6 text-center">
          <p className="text-bodydark">
            If you have any questions about your payment or order, please{" "}
            <Link to="/contact" className="text-primary hover:underline">
              contact our support team
            </Link>
            .
          </p>
        </div>
      </div>
    </div>
  );
};

export default InvoiceClientSuccessPage;
