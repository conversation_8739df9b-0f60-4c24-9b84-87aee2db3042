import React, { useState, useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "Src/globalContext";
import { AuthContext } from "Src/authContext";
import { AlertCircle } from "lucide-react";

const InvoiceClientCancelPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state: authState } = React.useContext(AuthContext);
  const [loading, setLoading] = useState(true);

  const invoiceId = searchParams.get("invoice_id");

  useEffect(() => {
    // Check if we have an invoice_id parameter
    if (invoiceId) {
      showToast(
        globalDispatch,
        "Payment was cancelled. You can try again anytime.",
        5000,
        "warning"
      );
    } else {
      // If no invoice_id, redirect to login
      navigate("/login");
    }
    setLoading(false);
  }, [invoiceId, navigate, globalDispatch]);

  const handleBackToInvoice = () => {
    // Check if user is authenticated
    if (!authState.isAuthenticated) {
      // If not logged in (public), don't redirect anywhere specific
      return;
    } else {
      // If logged in, redirect to the specific invoice page
      navigate(`/client/invoice/${invoiceId}`);
    }
  };

  const handleTryAgain = () => {
    // Check if user is authenticated
    if (!authState.isAuthenticated) {
      // If not logged in (public), redirect to the public invoice link
      window.location.href = `/invoice/${invoiceId}`;
    } else {
      // If logged in, redirect to the specific invoice page
      navigate(`/client/invoice/${invoiceId}`);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="w-16 h-16 rounded-full border-4 animate-spin border-primary border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="container p-4 mx-auto">
      <div className="flex flex-col justify-center items-center h-screen">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-4 rounded-full bg-warning/20">
              <AlertCircle className="w-16 h-16 text-warning" />
            </div>
          </div>
          <h1 className="mb-4 text-4xl font-bold text-warning">
            Payment Cancelled
          </h1>
          <p className="mb-6 text-lg text-bodydark">
            Your payment was cancelled. No charges were made to your account.
          </p>
          <div className="flex gap-4 justify-center">
            <button
              onClick={handleTryAgain}
              className="px-6 py-3 text-white rounded bg-primary hover:bg-opacity-90"
            >
              Try Again
            </button>
            <button
              onClick={handleBackToInvoice}
              className="px-6 py-3 text-white rounded border border-stroke hover:bg-meta-4"
            >
              {!authState.isAuthenticated ? "Close" : "Back to Invoice"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceClientCancelPage;
